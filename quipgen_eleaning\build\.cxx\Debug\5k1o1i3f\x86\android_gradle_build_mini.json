{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Task_Project\\quipgen_eleaning\\build\\.cxx\\Debug\\5k1o1i3f\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Task_Project\\quipgen_eleaning\\build\\.cxx\\Debug\\5k1o1i3f\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}