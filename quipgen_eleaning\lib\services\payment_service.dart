import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart'; // Temporarily disabled
import '../models/course.dart';
import 'api_service.dart';

// Mock payment response classes for compatibility
class PaymentSuccessResponse {
  final String? paymentId;
  final String? orderId;
  final String? signature;

  PaymentSuccessResponse({this.paymentId, this.orderId, this.signature});
}

class PaymentFailureResponse {
  final int code;
  final String message;
  final dynamic data;

  PaymentFailureResponse(this.code, this.message, this.data);
}

class ExternalWalletResponse {
  final String? walletName;

  ExternalWalletResponse({this.walletName});
}

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  late Razorpay _razorpay;
  bool _isInitialized = false;

  // Callbacks for payment events
  Function(PaymentSuccessResponse)? _onPaymentSuccess;
  Function(PaymentFailureResponse)? _onPaymentError;
  Function(ExternalWalletResponse)? _onExternalWallet;

  // Current payment context
  int? _currentCourseId;
  String? _currentOrderId;

  void initialize() {
    if (_isInitialized) return;

    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);

    print('Razorpay payment service initialized');
    _isInitialized = true;
  }

  void dispose() {
    if (_isInitialized) {
      _razorpay.clear();
    }
  }

  Future<void> purchaseCourse({
    required Course course,
    required String userEmail,
    required String userPhone,
    required Function(PaymentSuccessResponse) onSuccess,
    required Function(PaymentFailureResponse) onError,
    Function(ExternalWalletResponse)? onExternalWallet,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception(
          'Payment service not initialized. Call initialize() first.',
        );
      }

      // Set callbacks
      _onPaymentSuccess = onSuccess;
      _onPaymentError = onError;
      _onExternalWallet = onExternalWallet;

      // Store current course context
      _currentCourseId = int.tryParse(course.id);
      if (_currentCourseId == null) {
        throw Exception('Invalid course ID: ${course.id}');
      }

      // Step 1: Create payment order with backend
      final orderData = await ApiService().createPaymentOrder(
        _currentCourseId!,
      );
      _currentOrderId = orderData['orderId'];

      // Step 2: Open Razorpay checkout
      final options = {
        'key': 'rzp_test_1DP5mmOlF5G5ag', // Replace with your Razorpay key
        'amount': orderData['amount'],
        'currency': orderData['currency'] ?? 'INR',
        'name': 'QuipGen E-Learning',
        'description': course.title,
        'order_id': orderData['orderId'],
        'prefill': {'contact': userPhone, 'email': userEmail},
        'theme': {'color': '#6137C4'},
      };

      _razorpay.open(options);
    } catch (e) {
      _onPaymentError?.call(
        PaymentFailureResponse(1, 'Payment verification failed: $e', null),
      );
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    try {
      if (_currentCourseId == null || _currentOrderId == null) {
        throw Exception('Payment context lost');
      }

      // Step 3: Verify payment with backend
      final verificationResult = await ApiService().verifyPayment(
        razorpayOrderId: response.orderId!,
        razorpayPaymentId: response.paymentId!,
        razorpaySignature: response.signature!,
        courseId: _currentCourseId!,
      );

      print('Payment verification successful: $verificationResult');

      // Clear context
      _clearPaymentContext();

      // Call success callback
      _onPaymentSuccess?.call(response);
    } catch (e) {
      print('Payment verification failed: $e');
      _onPaymentError?.call(
        PaymentFailureResponse(1, 'Payment verification failed: $e', null),
      );
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('Payment failed: ${response.code} - ${response.message}');
    _clearPaymentContext();
    _onPaymentError?.call(response);
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('External wallet selected: ${response.walletName}');
    _onExternalWallet?.call(response);
  }

  void _clearPaymentContext() {
    _currentCourseId = null;
    _currentOrderId = null;
    _onPaymentSuccess = null;
    _onPaymentError = null;
    _onExternalWallet = null;
  }

  // Helper method to show payment result dialog
  static void showPaymentResultDialog({
    required BuildContext context,
    required bool isSuccess,
    required String message,
    VoidCallback? onOk,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.green : Colors.red,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                isSuccess ? 'Payment Successful' : 'Payment Failed',
                style: TextStyle(
                  color: isSuccess ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onOk?.call();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // Helper method to format amount for display
  static String formatAmount(double amount, String currency) {
    switch (currency.toUpperCase()) {
      case 'INR':
        return '₹${amount.toStringAsFixed(2)}';
      case 'USD':
        return '\$${amount.toStringAsFixed(2)}';
      default:
        return '${amount.toStringAsFixed(2)} $currency';
    }
  }

  void disposePaymentService() {
    if (_isInitialized) {
      _razorpay.clear();
      _isInitialized = false;
    }
    _clearPaymentContext();
  }
}

// Extension to add payment-related methods to Course model
extension CoursePayment on Course {
  int get amountInPaise => (price * 100).round();

  String get formattedPriceINR => '₹${price.toStringAsFixed(2)}';

  bool get isPaid => price > 0;

  bool get isFree => price == 0;
}
