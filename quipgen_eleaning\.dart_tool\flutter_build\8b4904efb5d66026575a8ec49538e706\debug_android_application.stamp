{"inputs": ["D:\\Task_Project\\quipgen_eleaning\\.dart_tool\\flutter_build\\8b4904efb5d66026575a8ec49538e706\\app.dill", "C:\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "C:\\flutter\\bin\\cache\\engine.stamp", "C:\\flutter\\bin\\cache\\engine.stamp", "C:\\flutter\\bin\\cache\\engine.stamp", "C:\\flutter\\bin\\cache\\engine.stamp", "D:\\Task_Project\\quipgen_eleaning\\pubspec.yaml", "D:\\Task_Project\\quipgen_eleaning\\assets\\onboarding\\phone-sign.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\onboarding\\step1.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\onboarding\\step2.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\onboarding\\step3.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\icons\\facebook.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\icons\\google.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\icons\\signup.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\icons\\signup1.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\course_images\\digital-marketing.jpg", "D:\\Task_Project\\quipgen_eleaning\\assets\\course_images\\flutter-course.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\course_images\\python-course.png", "D:\\Task_Project\\quipgen_eleaning\\assets\\course_images\\ux_course.png", "D:\\PubCache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\PubCache\\hosted\\pub.dev\\fluttertoast-8.2.8\\assets\\toastify.css", "D:\\PubCache\\hosted\\pub.dev\\fluttertoast-8.2.8\\assets\\toastify.js", "D:\\PubCache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-brands-400.ttf", "D:\\PubCache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-regular-400.ttf", "D:\\PubCache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\lib\\fonts\\fa-solid-900.ttf", "C:\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\Task_Project\\quipgen_eleaning\\.dart_tool\\flutter_build\\8b4904efb5d66026575a8ec49538e706\\native_assets.json", "C:\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\flutter\\packages\\flutter\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\_flutterfire_internals-1.3.35\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\connectivity_plus-6.1.4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\connectivity_plus_platform_interface-2.0.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\eventify-1.0.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\firebase_auth-4.20.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\firebase_auth_platform_interface-7.3.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\firebase_auth_web-5.12.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\firebase_core-2.32.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\firebase_core_platform_interface-5.4.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\firebase_core_web-2.17.5\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\fluttertoast-8.2.8\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\font_awesome_flutter-10.8.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_fonts-6.3.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_identity_services_web-0.3.3+1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_sign_in-6.3.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_sign_in_android-6.2.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_sign_in_ios-5.9.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_sign_in_platform_interface-2.5.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\google_sign_in_web-0.12.4+4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\nm-0.5.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\razorpay_flutter-1.4.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\sqflite_common-2.5.6\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\synchronized-3.4.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\web-0.5.1\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "D:\\PubCache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "D:\\Task_Project\\quipgen_eleaning\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD557431451"], "outputs": ["D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\onboarding\\phone-sign.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\onboarding\\step1.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\onboarding\\step2.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\onboarding\\step3.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\facebook.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\google.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\signup.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\icons\\signup1.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\course_images\\digital-marketing.jpg", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\course_images\\flutter-course.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\course_images\\python-course.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\course_images\\ux_course.png", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\fluttertoast\\assets\\toastify.css", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\fluttertoast\\assets\\toastify.js", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\font_awesome_flutter\\lib\\fonts\\fa-brands-400.ttf", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\font_awesome_flutter\\lib\\fonts\\fa-regular-400.ttf", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\font_awesome_flutter\\lib\\fonts\\fa-solid-900.ttf", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\Task_Project\\quipgen_eleaning\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}