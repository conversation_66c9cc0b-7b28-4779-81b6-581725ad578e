{"version": 2, "entries": [{"package": "shared_preferences", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences-2.5.3/", "packageUri": "lib/", "config": {"name": "shared_preferences", "issueTracker": "https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+shared_preferences%22", "version": "1.0.0", "materialIconCodePoint": "0xe683"}}, {"package": "quipgen_eleaning", "rootUri": "../", "packageUri": "lib/"}]}