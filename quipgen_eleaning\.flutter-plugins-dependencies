{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "connectivity_plus", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.20.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.8\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\google_sign_in_ios-5.9.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\razorpay_flutter-1.4.0\\\\", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "connectivity_plus", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.20.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.8\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\google_sign_in_android-6.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\razorpay_flutter-1.4.0\\\\", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\sqflite_android-2.4.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "connectivity_plus", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.20.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\google_sign_in_ios-5.9.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\sqflite_darwin-2.4.2\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "connectivity_plus", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-6.1.4\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "connectivity_plus", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-6.1.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.20.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "connectivity_plus", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\connectivity_plus-6.1.4\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_auth_web-5.12.0\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.17.5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\fluttertoast-8.2.8\\\\", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\google_sign_in_web-0.12.4+4\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "D:\\\\PubCache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "connectivity_plus", "dependencies": []}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "razorpay_flutter", "dependencies": ["fluttertoast"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}], "date_created": "2025-08-04 09:02:45.927566", "version": "3.32.7", "swift_package_manager_enabled": {"ios": false, "macos": false}}