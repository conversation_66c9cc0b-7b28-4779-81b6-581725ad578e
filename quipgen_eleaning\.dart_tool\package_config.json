{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///D:/PubCache/hosted/pub.dev/_flutterfire_internals-1.3.35", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "args", "rootUri": "file:///D:/PubCache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///D:/PubCache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///D:/PubCache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///D:/PubCache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///D:/PubCache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///D:/PubCache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "connectivity_plus", "rootUri": "file:///D:/PubCache/hosted/pub.dev/connectivity_plus-6.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "connectivity_plus_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "crypto", "rootUri": "file:///D:/PubCache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///D:/PubCache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dbus", "rootUri": "file:///D:/PubCache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "eventify", "rootUri": "file:///D:/PubCache/hosted/pub.dev/eventify-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///D:/PubCache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///D:/PubCache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///D:/PubCache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "firebase_auth", "rootUri": "file:///D:/PubCache/hosted/pub.dev/firebase_auth-4.20.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/firebase_auth_platform_interface-7.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_web", "rootUri": "file:///D:/PubCache/hosted/pub.dev/firebase_auth_web-5.12.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core", "rootUri": "file:///D:/PubCache/hosted/pub.dev/firebase_core-2.32.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/firebase_core_platform_interface-5.4.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///D:/PubCache/hosted/pub.dev/firebase_core_web-2.17.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter", "rootUri": "file:///C:/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_lints", "rootUri": "file:///D:/PubCache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///C:/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "fluttertoast", "rootUri": "file:///D:/PubCache/hosted/pub.dev/fluttertoast-8.0.8", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "font_awesome_flutter", "rootUri": "file:///D:/PubCache/hosted/pub.dev/font_awesome_flutter-10.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "google_fonts", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_fonts-6.3.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "google_identity_services_web", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_identity_services_web-0.3.3+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_sign_in-6.3.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_android", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_sign_in_android-6.2.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_sign_in_ios", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_sign_in_ios-5.9.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_web", "rootUri": "file:///D:/PubCache/hosted/pub.dev/google_sign_in_web-0.12.4+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http", "rootUri": "file:///D:/PubCache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///D:/PubCache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "leak_tracker", "rootUri": "file:///D:/PubCache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///D:/PubCache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///D:/PubCache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///D:/PubCache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///D:/PubCache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///D:/PubCache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///D:/PubCache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nm", "rootUri": "file:///D:/PubCache/hosted/pub.dev/nm-0.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///D:/PubCache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "petitparser", "rootUri": "file:///D:/PubCache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "platform", "rootUri": "file:///D:/PubCache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "razorpay_flutter", "rootUri": "file:///D:/PubCache/hosted/pub.dev/razorpay_flutter-1.4.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shared_preferences", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///D:/PubCache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///C:/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///D:/PubCache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sqflite", "rootUri": "file:///D:/PubCache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///D:/PubCache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///D:/PubCache/hosted/pub.dev/sqflite_common-2.5.6", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "sqflite_darwin", "rootUri": "file:///D:/PubCache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///D:/PubCache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///D:/PubCache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///D:/PubCache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///D:/PubCache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///D:/PubCache/hosted/pub.dev/synchronized-3.4.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "term_glyph", "rootUri": "file:///D:/PubCache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///D:/PubCache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///D:/PubCache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///D:/PubCache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///D:/PubCache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///D:/PubCache/hosted/pub.dev/web-0.5.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xdg_directories", "rootUri": "file:///D:/PubCache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///D:/PubCache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "quipgen_eleaning", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///C:/flutter", "flutterVersion": "3.32.7", "pubCache": "file:///D:/PubCache"}