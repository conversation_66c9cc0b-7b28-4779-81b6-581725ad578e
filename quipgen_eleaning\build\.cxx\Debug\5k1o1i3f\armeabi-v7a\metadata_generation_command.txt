                        -HC:\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-D<PERSON>DROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Task_Project\quipgen_eleaning\build\app\intermediates\cxx\Debug\5k1o1i3f\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Task_Project\quipgen_eleaning\build\app\intermediates\cxx\Debug\5k1o1i3f\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\Task_Project\quipgen_eleaning\build\.cxx\Debug\5k1o1i3f\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2