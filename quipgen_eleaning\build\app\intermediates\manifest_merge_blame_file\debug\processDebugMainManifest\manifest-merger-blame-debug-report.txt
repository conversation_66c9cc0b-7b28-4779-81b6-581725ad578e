1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quipgen.elearning"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:3:5-67
15-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:4:5-79
16-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:4:22-76
17    <!--
18 Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:52:5-57:15
25        <intent>
25-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:53:9-56:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:13-72
26-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:54:21-70
27
28            <data android:mimeType="text/plain" />
28-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:55:13-50
28-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:55:19-48
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:5-68
32-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:24:22-65
33    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
33-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
33-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
34    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
34-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
34-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
35    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
35-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
35-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
36    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
36-->[com.google.android.recaptcha:recaptcha:18.4.0] D:\Gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
36-->[com.google.android.recaptcha:recaptcha:18.4.0] D:\Gradle\caches\8.12\transforms\eb1aaf19147ba82d0b9e78620bffbaa8\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
37    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
37-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
37-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
38
39    <permission
39-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
40        android:name="com.quipgen.elearning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
40-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
41        android:protectionLevel="signature" />
41-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
42
43    <uses-permission android:name="com.quipgen.elearning.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
43-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
43-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
44
45    <application
46        android:name="android.app.Application"
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] D:\Gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:debuggable="true"
49        android:enableOnBackInvokedCallback="true"
50        android:extractNativeLibs="true"
51        android:icon="@mipmap/ic_launcher"
52        android:label="quipgen_eleaning"
53        android:usesCleartextTraffic="true" >
54        <activity
55            android:name="com.quipgen.elearning.MainActivity"
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57            android:exported="true"
58            android:hardwareAccelerated="true"
59            android:launchMode="singleTop"
60            android:taskAffinity=""
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87
88        <!-- Disable automatic Firebase initialization -->
89        <provider
90            android:name="com.google.firebase.provider.FirebaseInitProvider"
91            android:authorities="com.quipgen.elearning.firebaseinitprovider"
92            android:directBootAware="true"
92-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
93            android:enabled="false"
94            android:exported="false"
95            android:initOrder="100" />
95-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
96
97        <service
97-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
98            android:name="com.google.firebase.components.ComponentDiscoveryService"
98-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
99            android:directBootAware="true"
99-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
100            android:exported="false" >
100-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:10:13-37
101            <meta-data
101-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
102                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
102-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[:firebase_auth] D:\Task_Project\quipgen_eleaning\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
104            <meta-data
104-->[:firebase_core] D:\Task_Project\quipgen_eleaning\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
105                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
105-->[:firebase_core] D:\Task_Project\quipgen_eleaning\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[:firebase_core] D:\Task_Project\quipgen_eleaning\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
107            <meta-data
107-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:11:13-13:85
108                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
108-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:12:17-119
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-auth-ktx:22.3.1] D:\Gradle\caches\8.12\transforms\1b00888fe10f74f3e7b67235eb6c8a60\transformed\jetified-firebase-auth-ktx-22.3.1\AndroidManifest.xml:13:17-82
110            <meta-data
110-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
111                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
111-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
113            <meta-data
113-->[com.google.firebase:firebase-analytics-ktx:21.6.1] D:\Gradle\caches\8.12\transforms\1c7efed489efea967d9fe7b470ea80e1\transformed\jetified-firebase-analytics-ktx-21.6.1\AndroidManifest.xml:11:13-13:85
114                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
114-->[com.google.firebase:firebase-analytics-ktx:21.6.1] D:\Gradle\caches\8.12\transforms\1c7efed489efea967d9fe7b470ea80e1\transformed\jetified-firebase-analytics-ktx-21.6.1\AndroidManifest.xml:12:17-129
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.firebase:firebase-analytics-ktx:21.6.1] D:\Gradle\caches\8.12\transforms\1c7efed489efea967d9fe7b470ea80e1\transformed\jetified-firebase-analytics-ktx-21.6.1\AndroidManifest.xml:13:17-82
116            <meta-data
116-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
117                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
117-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
119            <meta-data
119-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
120                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
120-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
122            <meta-data
122-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
123                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
123-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-installations:17.2.0] D:\Gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common-ktx:20.4.3] D:\Gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
126                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
126-->[com.google.firebase:firebase-common-ktx:20.4.3] D:\Gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common-ktx:20.4.3] D:\Gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
128            <meta-data
128-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
129                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
129-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-common:20.4.3] D:\Gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
131        </service>
132
133        <activity
133-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
134            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
134-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
135            android:excludeFromRecents="true"
135-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
136            android:exported="true"
136-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
137            android:launchMode="singleTask"
137-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
139            <intent-filter>
139-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
140                <action android:name="android.intent.action.VIEW" />
140-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
140-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
142-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
143                <category android:name="android.intent.category.BROWSABLE" />
143-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
143-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
144
145                <data
145-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:55:13-50
146                    android:host="firebase.auth"
147                    android:path="/"
148                    android:scheme="genericidp" />
149            </intent-filter>
150        </activity>
151        <activity
151-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
152            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
152-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
153            android:excludeFromRecents="true"
153-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
154            android:exported="true"
154-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
155            android:launchMode="singleTask"
155-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
156-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
157            <intent-filter>
157-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
158                <action android:name="android.intent.action.VIEW" />
158-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
158-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
160-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
161                <category android:name="android.intent.category.BROWSABLE" />
161-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
161-->[com.google.firebase:firebase-auth:22.3.1] D:\Gradle\caches\8.12\transforms\49bda5d7462ff6887f7ce637bcef0544\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
162
163                <data
163-->D:\Task_Project\quipgen_eleaning\android\app\src\main\AndroidManifest.xml:55:13-50
164                    android:host="firebase.auth"
165                    android:path="/"
166                    android:scheme="recaptcha" />
167            </intent-filter>
168        </activity>
169        <activity
169-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
170            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
170-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
171            android:excludeFromRecents="true"
171-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
172            android:exported="false"
172-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
173-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
174        <!--
175            Service handling Google Sign-In user revocation. For apps that do not integrate with
176            Google Sign-In, this service will never be started.
177        -->
178        <service
178-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
179            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
179-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
180            android:exported="true"
180-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
181            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
181-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
182            android:visibleToInstantApps="true" />
182-->[com.google.android.gms:play-services-auth:21.0.0] D:\Gradle\caches\8.12\transforms\cef35d8b5b0c95aad5d18440571cb292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
183
184        <activity
184-->[com.google.android.gms:play-services-base:18.0.1] D:\Gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
185            android:name="com.google.android.gms.common.api.GoogleApiActivity"
185-->[com.google.android.gms:play-services-base:18.0.1] D:\Gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
186            android:exported="false"
186-->[com.google.android.gms:play-services-base:18.0.1] D:\Gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
187            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
187-->[com.google.android.gms:play-services-base:18.0.1] D:\Gradle\caches\8.12\transforms\a75f01a7707dc356bd2b3ef3fe83d0a8\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
188
189        <property
189-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
190            android:name="android.adservices.AD_SERVICES_CONFIG"
190-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
191            android:resource="@xml/ga_ad_services_config" />
191-->[com.google.android.gms:play-services-measurement-api:21.6.1] D:\Gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
192
193        <receiver
193-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
194            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
194-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
195            android:enabled="true"
195-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
196            android:exported="false" >
196-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
197        </receiver>
198
199        <service
199-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
200            android:name="com.google.android.gms.measurement.AppMeasurementService"
200-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
201            android:enabled="true"
201-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
202            android:exported="false" />
202-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
203        <service
203-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
204            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
204-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
205            android:enabled="true"
205-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
206            android:exported="false"
206-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
207            android:permission="android.permission.BIND_JOB_SERVICE" />
207-->[com.google.android.gms:play-services-measurement:21.6.1] D:\Gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
208
209        <uses-library
209-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
210            android:name="androidx.window.extensions"
210-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
211            android:required="false" />
211-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
212        <uses-library
212-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
213            android:name="androidx.window.sidecar"
213-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
214            android:required="false" />
214-->[androidx.window:window:1.2.0] D:\Gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
215        <uses-library
215-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] D:\Gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
216            android:name="android.ext.adservices"
216-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] D:\Gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
217            android:required="false" />
217-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] D:\Gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
218
219        <meta-data
219-->[com.google.android.gms:play-services-basement:18.3.0] D:\Gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
220            android:name="com.google.android.gms.version"
220-->[com.google.android.gms:play-services-basement:18.3.0] D:\Gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
221            android:value="@integer/google_play_services_version" />
221-->[com.google.android.gms:play-services-basement:18.3.0] D:\Gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
222
223        <provider
223-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
224            android:name="androidx.startup.InitializationProvider"
224-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
225            android:authorities="com.quipgen.elearning.androidx-startup"
225-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
226            android:exported="false" >
226-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
227            <meta-data
227-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
228                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
228-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
229                android:value="androidx.startup" />
229-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\Gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
230            <meta-data
230-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
231                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
232                android:value="androidx.startup" />
232-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
233        </provider>
234
235        <receiver
235-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
236            android:name="androidx.profileinstaller.ProfileInstallReceiver"
236-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
237            android:directBootAware="false"
237-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
238            android:enabled="true"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
239            android:exported="true"
239-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
240            android:permission="android.permission.DUMP" >
240-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
241            <intent-filter>
241-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
242                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
242-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
242-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
243            </intent-filter>
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
245                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
245-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
245-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
248                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
248-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
251                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
251-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
251-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
252            </intent-filter>
253        </receiver>
254    </application>
255
256</manifest>
